import { User } from '@/lib/types';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Badge } from '../ui/badge';
import { UserStatus } from '../chat/UserStatus';

interface ProjectMembersProps {
  members: User[];
}

export function ProjectMembers({ members }: ProjectMembersProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Project Members</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {members.map(member => (
            <div key={member.id} className="flex items-center gap-3">
              <Avatar>
                <AvatarImage src={member.profileImage} />
                <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">{member.name}</span>
                  <Badge variant="outline" className="capitalize">
                    {member.role}
                  </Badge>
                </div>
                <UserStatus userId={member.id} />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
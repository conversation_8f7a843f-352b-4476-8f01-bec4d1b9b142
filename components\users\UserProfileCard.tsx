import { User } from '@/lib/types';
import { <PERSON>, CardHeader, CardContent } from '../ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Badge } from '../ui/badge';
import { UserStatus } from '../chat/UserStatus';

interface UserProfileCardProps {
  user: User;
}

export function UserProfileCard({ user }: UserProfileCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center gap-4">
        <Avatar className="h-12 w-12">
          <AvatarImage src={user.profileImage} alt={user.name} />
          <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div>
          <h3 className="text-lg font-semibold">{user.name}</h3>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="capitalize">
              {user.role}
            </Badge>
            <UserStatus userId={user.id} />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {user.bio && <p className="text-muted-foreground">{user.bio}</p>}
        {user.department && (
          <p className="text-sm text-muted-foreground mt-2">
            Department: {user.department}
          </p>
        )}
        {user.specialization && (
          <p className="text-sm text-muted-foreground">
            Specialization: {user.specialization}
          </p>
        )}
      </CardContent>
    </Card>
  );
}
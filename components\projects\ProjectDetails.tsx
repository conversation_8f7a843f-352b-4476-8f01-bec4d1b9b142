'use client';

import { Head<PERSON> } from '@/components/layout/Header';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { sampleUsers, sampleDocuments } from '@/lib/sample';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Calendar, Clock, FileText, MessageCircle, Users } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Project } from '@/lib/types';

interface ProjectDetailsProps {
  project: Project;
}

export function ProjectDetails({ project }: ProjectDetailsProps) {
  const router = useRouter();
  const student = sampleUsers.find((user) => user.id === project.studentId);
  const supervisors = project.supervisorIds
    .map((id) => sampleUsers.find((user) => user.id === id))
    .filter(Boolean);
  const documents = sampleDocuments.filter(
    (doc) => doc.studentId === project.studentId
  );

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="flex-1 flex justify-center">
        <div className="w-full max-w-7xl px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
                {project.title}
              </h1>
              <div className="flex items-center gap-2 text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span className="text-sm">
                  Created {format(project.createdAt, 'MMMM d, yyyy')}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => router.push('/chat')}>
                <MessageCircle className="h-4 w-4 mr-2" />
                Message
              </Button>
              <Button>View Timeline</Button>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-3">
            <div className="md:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Project Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <p className="text-muted-foreground">
                      {project.description}
                    </p>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Progress</span>
                        <span className="font-medium">60%</span>
                      </div>
                      <Progress value={60} />
                    </div>
                    <div className="grid gap-4 sm:grid-cols-3">
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                          <CardTitle className="text-sm font-medium">
                            Documents
                          </CardTitle>
                          <FileText className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{documents.length}</div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                          <CardTitle className="text-sm font-medium">
                            Supervisors
                          </CardTitle>
                          <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{supervisors.length}</div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between pb-2">
                          <CardTitle className="text-sm font-medium">
                            Time Active
                          </CardTitle>
                          <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">3m</div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Tabs defaultValue="documents" className="space-y-4">
                <TabsList>
                  <TabsTrigger value="documents">Documents</TabsTrigger>
                  <TabsTrigger value="timeline">Timeline</TabsTrigger>
                </TabsList>

                <TabsContent value="documents">
                  <Card>
                    <CardHeader>
                      <CardTitle>Project Documents</CardTitle>
                      <CardDescription>
                        All documents submitted for this project
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {documents.map((doc) => (
                          <div
                            key={doc.id}
                            className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                            onClick={() => router.push(`/documents/${doc.id}`)}
                          >
                            <div>
                              <p className="font-medium">{doc.title}</p>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <span>Last updated {format(doc.updatedAt, 'PP')}</span>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant="secondary" className="capitalize">
                                {doc.type}
                              </Badge>
                              <Badge
                                variant={
                                  doc.status === 'approved'
                                    ? 'default'
                                    : doc.status === 'under_review'
                                    ? 'secondary'
                                    : 'destructive'
                                }
                                className="capitalize"
                              >
                                {doc.status.replace('_', ' ')}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="timeline">
                  <Card>
                    <CardHeader>
                      <CardTitle>Project Timeline</CardTitle>
                      <CardDescription>
                        Major events and milestones
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {documents.map((doc) => (
                          <div
                            key={doc.id}
                            className="flex gap-4 relative pb-4 last:pb-0"
                          >
                            <div className="w-4 h-4 rounded-full bg-primary mt-1" />
                            <div className="flex-1">
                              <p className="font-medium">{doc.title} submitted</p>
                              <p className="text-sm text-muted-foreground">
                                {format(doc.createdAt, 'PPp')}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Student</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={student?.profileImage} alt={student?.name} />
                      <AvatarFallback>{student?.name?.slice(0, 2)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{student?.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {student?.department}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Supervisors</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {supervisors.map((supervisor) => (
                      <div
                        key={supervisor?.id}
                        className="flex items-center space-x-4"
                      >
                        <Avatar className="h-10 w-10">
                          <AvatarImage
                            src={supervisor?.profileImage}
                            alt={supervisor?.name}
                          />
                          <AvatarFallback>
                            {supervisor?.name?.slice(0, 2)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{supervisor?.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {supervisor?.specialization}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Project Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium">Status</p>
                      <Badge className="mt-1 capitalize">{project.status}</Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Created</p>
                      <p className="text-sm text-muted-foreground">
                        {format(project.createdAt, 'PPP')}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Last Updated</p>
                      <p className="text-sm text-muted-foreground">
                        {format(project.updatedAt, 'PPP')}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}